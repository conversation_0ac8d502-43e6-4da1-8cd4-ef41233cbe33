/* 토스 스타일 커스텀 CSS */
@import url('https://fonts.googleapis.com/css2?family=Pretendard:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --toss-blue: #3182f6;
  --toss-blue-light: #4593fc;
  --toss-blue-dark: #1b64da;
  --toss-gray-50: #f9fafb;
  --toss-gray-100: #f3f4f6;
  --toss-gray-200: #e5e7eb;
  --toss-gray-300: #d1d5db;
  --toss-gray-400: #9ca3af;
  --toss-gray-500: #6b7280;
  --toss-gray-600: #4b5563;
  --toss-gray-700: #374151;
  --toss-gray-800: #1f2937;
  --toss-gray-900: #111827;
}

body {
  font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, system-ui, Roboto, 'Helvetica Neue', 'Segoe UI', 'Apple SD Gothic Neo', 'Noto Sans KR', 'Malgun Gothic', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 토스 스타일 카드 */
.toss-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200;
}

/* 토스 스타일 버튼 */
.toss-btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
}

.toss-btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-6 py-3 rounded-xl transition-all duration-200;
}

/* 토스 스타일 그라데이션 */
.toss-gradient {
  background: linear-gradient(135deg, #3182f6 0%, #1d4ed8 100%);
}

/* 부드러운 애니메이션 */
.smooth-transition {
  @apply transition-all duration-300 ease-in-out;
}

/* 토스 스타일 그림자 */
.toss-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.toss-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
